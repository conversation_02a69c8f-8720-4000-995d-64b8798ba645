﻿{
  "manifest_version": 3,
  "name": "[秘灵]自动助手",
  "version": "2.0.0",
  "description": "[秘灵]自动助手",
  "permissions": [
    "activeTab",
    "tabs",
    "windows",
    "scripting",
    "storage"
  ],
  "host_permissions": [
    "<all_urls>"
  ],
  "action": {
    "default_popup": "plugin-automation-popup.html",
    "default_icon": {
      "16": "icons/icon16.png",
      "48": "icons/icon48.png",
      "128": "icons/icon128.png"
    }
  },
  "background": {
    "service_worker": "background/background.js"
  },
  "content_scripts": [
    {
      "matches": [
        "<all_urls>"
      ],
      "js": [
        "content/content.js"
      ]
    }
  ],
  "icons": {
    "16": "icons/icon16.png",
    "48": "icons/icon48.png",
    "128": "icons/icon128.png"
  },
  "web_accessible_resources": [
    {
      "resources": [
        "universal-automation-engine.js",
        "workflow-manager.js",
        "workflow-designer.html",
        "workflow-designer.js",
        "workflow-designer-cytoscape.js",
        "workflow-designer-mxgraph.html",
        "workflow-designer-mxgraph.js",
        "js/logic-flow.js",
        "js/mxClient.min.js",
        "content/content.js",
        "modules/content/content-core.js",
        "modules/content/sensitive-word-detector.js",
        "modules/content/content-automation.js",
        "modules/content/floating-control-panel.js",
        "modules/designer/designer-core.js",
        "modules/designer/designer-nodes.js",
        "modules/designer/designer-workflow.js",
        "modules/window/window-manager.js",
        "modules/window/action-executor.js",
        "modules/window/window-step-types.js",
        "modules/popup/popup-window-manager.js"
      ],
      "matches": [
        "<all_urls>"
      ]
    }
  ]
}