# 图标文件

插件需要以下尺寸的图标:

- icon16.png (16x16)
- icon48.png (48x48)
- icon128.png (128x128)

## 获取图标的方法

1. 你可以使用在线图标生成器创建简单的图标，例如:

   - [https://favicon.io/](https://favicon.io/)
   - [https://www.canva.com/](https://www.canva.com/)

2. 或者使用以下材料图标作为基础:

   - 搜索图标: https://fonts.google.com/icons?selected=Material+Icons:search
   - 天气图标: https://fonts.google.com/icons?selected=Material+Icons:cloud

3. 将生成的图标文件保存在此目录中，并确保它们的名称与 manifest.json 中定义的一致。

## 临时图标解决方案

如果你暂时没有图标，也可以使用在线服务生成简单的占位图标:

- 访问 https://placeholder.com/ 并下载所需尺寸的占位图像
- 重命名为所需的文件名放在此目录
