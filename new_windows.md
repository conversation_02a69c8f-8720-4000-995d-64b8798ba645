我想给当前项目加一个功能，使用场景是：在一些页面上点击某个按钮，页面会打开一个新窗口，我们的插件需要实现，在i点击操作里新加一个配置，告诉后续的程序，接下来会打开一个新窗口，剩下的步骤都是去新窗口执行，直到碰到关闭窗口的操作步骤（这个需要新增步骤），后续的操作才回到主界面执行，至于关闭窗口的功能就是关闭当前新开的窗口，回到主窗口去执行接下来的步骤，基于这个场景，规划如下：
1. 核心架构：分工明确
background.js (后台脚本): 大脑/总指挥

负责存储和管理完整的自动化步骤（JSON配置）。
负责按顺序执行步骤，是流程引擎的所在地。
负责监听和管理所有窗口（Tab）的状态，知道哪个是主窗口，哪个是新窗口。
负责动态地向指定窗口注入执行脚本。
action_executor.js (执行脚本): 手和脚

这是一个被动态注入的脚本，不是在manifest.json里配置的content_script。
它的任务非常简单：在当前页面中，根据background.js传来的指令（如selector），执行具体操作（如click()）。
popup.js / content.js (触发器)

负责启动整个自动化流程，比如用户点击插件图标的按钮，或者在特定页面上满足某些条件时自动触发。
2. 流程配置 (JSON)
步骤配置将存放在 background.js 中。
3. manifest.json 配置
确保权限正确，并注册好后台脚本。
4. background.js (核心引擎实现)
我们将使用 async/await 来完美处理异步流程，比如打开新窗口后再执行流程后面的操作
5. action_executor.js (被注入的执行脚本)
这个脚本负责在页面上执行具体操作。

总结与关键点
中央化引擎: 所有流程控制逻辑都在 background.js 中，它像一个总指挥，避免了状态混乱。
async/await: 这是处理异步流程的现代且最可靠的方式。for...of 循环可以正确地与 await 配合，确保上一步完成后再执行下一步。
使用 Promise 等待: 当你执行一个会打开新窗口的点击时，你不知道它何时完成。通过创建一个 Promise 来包装 chrome.tabs.onCreated 事件，你可以使用 await 暂停你的流程引擎，直到新窗口真的被创建。这是解决你问题的核心。
动态注入 (scripting.executeScript): 不要依赖静态的 content_scripts。动态注入让你可以在任何需要的时刻，向任何指定的窗口注入代码，这提供了极大的灵活性。
明确的通信: 使用 chrome.tabs.sendMessage 向注入的脚本发送具体指令，而不是把复杂逻辑写在注入脚本里。这让你的代码更易于维护。