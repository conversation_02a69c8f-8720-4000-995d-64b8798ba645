{"name": "基础新窗口流程模板", "description": "一个简单的新窗口管理流程模板，可以根据需要修改", "version": "1.0.0", "steps": [{"id": "step_1", "name": "主窗口：填写用户名", "type": "input", "action": "input", "locator": {"strategy": "css", "value": "#username"}, "text": "请修改为你的用户名", "description": "在主窗口输入用户名，请修改定位器和文本"}, {"id": "step_2", "name": "主窗口：点击打开新窗口", "type": "click", "action": "click", "opensNewWindow": true, "locator": {"strategy": "css", "value": "#open-new-window-btn"}, "newWindowTimeout": 10000, "windowReadyTimeout": 30000, "switchToNewWindow": true, "description": "点击按钮打开新窗口，请修改定位器为实际的按钮选择器"}, {"id": "step_3", "name": "新窗口：填写表单数据", "type": "input", "action": "input", "locator": {"strategy": "css", "value": "#new-window-input"}, "text": "请修改为新窗口中要输入的数据", "description": "在新窗口中输入数据，请修改定位器和文本"}, {"id": "step_4", "name": "新窗口：点击提交按钮", "type": "click", "action": "click", "locator": {"strategy": "css", "value": "#submit-btn"}, "description": "在新窗口中点击提交，请修改定位器"}, {"id": "step_5", "name": "新窗口：等待操作完成", "type": "smartWait", "action": "smartWait", "locator": {"strategy": "css", "value": "#success-message"}, "condition": "appear", "timeout": 15000, "description": "等待成功消息出现，请修改定位器为实际的成功提示元素"}, {"id": "step_6", "name": "关闭新窗口，返回主窗口", "type": "closeWindow", "action": "closeWindow", "closeTarget": "current", "returnToPrevious": true, "description": "关闭当前新窗口并自动返回到主窗口"}, {"id": "step_7", "name": "主窗口：继续后续操作", "type": "click", "action": "click", "locator": {"strategy": "css", "value": "#continue-btn"}, "description": "回到主窗口后继续执行，请修改定位器"}], "connections": [{"source": "step_1", "target": "step_2"}, {"source": "step_2", "target": "step_3"}, {"source": "step_3", "target": "step_4"}, {"source": "step_4", "target": "step_5"}, {"source": "step_5", "target": "step_6"}, {"source": "step_6", "target": "step_7"}], "settings": {"windowManagement": {"enabled": true, "autoSwitchToNewWindow": true, "autoCloseOnComplete": false, "maxWindowStack": 5}, "execution": {"stepDelay": 1000, "retryCount": 3, "timeout": 30000}}}